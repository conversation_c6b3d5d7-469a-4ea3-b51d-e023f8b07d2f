import { boolean, pgTable, text, timestamp } from "drizzle-orm/pg-core";

export const messages = pgTable("messages", {
	id: text("id").primaryKey(),
	message: text("message").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// export const posts = pgTable(
//   "blogs",
//   {
//     id: text("id").primaryKey(),
//     authorId: text("author_id") // Using serial for simplicity, typically foreign key to users.id
//       .notNull()
//       .references(() => users.id, { onDelete: "cascade" }), // Cascade delete if user is removed
//     title: text("title").notNull(),
//     slug: text("slug").notNull().unique(), // Unique slug for clean URLs
//     content: text("content").notNull(), // Markdown or HTML content
//     excerpt: text("excerpt"), // Short summary of the post
//     // status: postStatusEnum("status").notNull().default("draft"), // Using the enum
//     published: boolean("published").notNull().default(false), // Simple published status
//     tags: text("tags").array(), // Store tags as an array of strings (PostgreSQL specific)
//     imageUrl: text("image_url"), // Optional URL for a featured image
//     createdAt: timestamp("created_at").notNull().defaultNow(),
//     updatedAt: timestamp("updated_at").notNull().defaultNow(),
//     publishedAt: timestamp("published_at"), // When the post was actually published
//   },
//   (table) => {
//     return {
//       slugIdx: uniqueIndex("slug_idx").on(table.slug), // Ensure slug is unique
//       authorIdIdx: uniqueIndex("author_id_idx").on(table.authorId),
//     };
//   },
// );

export const user = pgTable("user", {
	id: text("id").primaryKey(),
	name: text("name").notNull(),
	email: text("email").notNull().unique(),
	emailVerified: boolean("email_verified")
		.$defaultFn(() => false)
		.notNull(),
	image: text("image"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

export const session = pgTable("session", {
	id: text("id").primaryKey(),
	expiresAt: timestamp("expires_at").notNull(),
	token: text("token").notNull().unique(),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
	ipAddress: text("ip_address"),
	userAgent: text("user_agent"),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
});

export const account = pgTable("account", {
	id: text("id").primaryKey(),
	accountId: text("account_id").notNull(),
	providerId: text("provider_id").notNull(),
	userId: text("user_id")
		.notNull()
		.references(() => user.id, { onDelete: "cascade" }),
	accessToken: text("access_token"),
	refreshToken: text("refresh_token"),
	idToken: text("id_token"),
	accessTokenExpiresAt: timestamp("access_token_expires_at"),
	refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
	scope: text("scope"),
	password: text("password"),
	createdAt: timestamp("created_at").notNull(),
	updatedAt: timestamp("updated_at").notNull(),
});

export const verification = pgTable("verification", {
	id: text("id").primaryKey(),
	identifier: text("identifier").notNull(),
	value: text("value").notNull(),
	expiresAt: timestamp("expires_at").notNull(),
	createdAt: timestamp("created_at").defaultNow(),
	updatedAt: timestamp("updated_at").defaultNow(),
});
