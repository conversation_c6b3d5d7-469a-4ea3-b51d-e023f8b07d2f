import { env } from "@server/env";
import { generateId } from "better-auth";
import { auth } from "../auth";
import { db } from ".";
import { account, user } from "./schema";

async function seed() {
	console.log("🌱 Seeding database...");

	const accountExists = await db.query.account.findFirst();
	const userExists = await db.query.user.findFirst();
	if (accountExists && userExists)
		return console.log("Database already seeded.");
	if (accountExists) {
		if (!userExists) {
			const data = await auth.api.accountInfo({
				body: { accountId: accountExists.id },
			});
			if (!data?.user) return;
			await db.insert(user).values({
				id: generateId(),
				name: data?.user.name ?? env.ROOT_USERNAME,
				email: data?.user.email ?? env.ROOT_EMAIL,
				emailVerified: data.user.emailVerified ?? true,
			});
		}
		return;
	}

	await auth.api
		.signUpEmail({
			body: {
				name: env.ROOT_USERNAME,
				email: env.ROOT_EMAIL,
				password: env.ROOT_PASSWORD,
				image:
					"https://catpics.lol/cat/0ba226345e8b52f33b062ce5bbd1c844ad0135c9d9cda885e212b30d819156b3.webp",
			},
		})
		.then(console.log);
}
seed()
	.catch((error) => {
		console.error("Error seeding database:", error);
		process.exit(1);
	})
	.finally(() => {
		process.exit();
	});
